"""
Test script for the simplified MentorProfile model
"""

from sqlalchemy.orm import Session
from database import engine
from Models.users import User, MentorProfile, Subject
from Schemas.Institute.Mentor import MentorRegistrationCreate
import uuid
import json


def test_mentor_creation():
    """Test creating a mentor with the new simplified model"""
    
    with Session(engine) as db:
        try:
            # Create some test subjects first
            subjects = []
            subject_names = ["Mathematics", "Physics", "Chemistry", "Biology"]
            
            for name in subject_names:
                existing = db.query(Subject).filter(Subject.name == name).first()
                if not existing:
                    subject = Subject(id=uuid.uuid4(), name=name)
                    db.add(subject)
                    subjects.append(subject)
                else:
                    subjects.append(existing)
            
            db.commit()
            
            # Create test mentor data
            mentor_data = {
                "username": "test_mentor",
                "email": "<EMAIL>",
                "mobile": "+1234567890",
                "password": "testpassword123",
                "country": "USA",
                "bio": "Experienced mentor in STEM subjects",
                "experience_years": 5,
                "hourly_rate": 50.00,
                "expertise_subject_ids": [subjects[0].id, subjects[1].id],  # Math, Physics
                "preferred_subject_ids": [subjects[0].id, subjects[2].id],  # Math, Chemistry
                "languages": ["English", "Spanish"],
                "availability_hours": {
                    "monday": ["09:00-12:00", "14:00-17:00"],
                    "tuesday": ["09:00-12:00"],
                    "wednesday": ["14:00-17:00"]
                }
            }
            
            # Test the mentor creation
            print("Testing mentor creation with simplified model...")
            
            # Create user
            user = User(
                id=uuid.uuid4(),
                username=mentor_data["username"],
                email=mentor_data["email"],
                mobile=mentor_data["mobile"],
                password_hash="hashed_password",  # In real app, this would be hashed
                country=mentor_data["country"],
                user_type="mentor"
            )
            
            db.add(user)
            db.flush()
            
            # Create mentor profile
            mentor_profile = MentorProfile(
                user_id=user.id,
                bio=mentor_data["bio"],
                experience_years=mentor_data["experience_years"],
                hourly_rate=mentor_data["hourly_rate"],
                languages=json.dumps(mentor_data["languages"]),
                availability_hours=json.dumps(mentor_data["availability_hours"])
            )
            
            db.add(mentor_profile)
            db.flush()
            
            # Add expertise subjects
            expertise_subjects = db.query(Subject).filter(
                Subject.id.in_(mentor_data["expertise_subject_ids"])
            ).all()
            mentor_profile.expertise_subjects.extend(expertise_subjects)
            
            # Add preferred subjects
            preferred_subjects = db.query(Subject).filter(
                Subject.id.in_(mentor_data["preferred_subject_ids"])
            ).all()
            mentor_profile.preferred_subjects.extend(preferred_subjects)
            
            db.commit()
            
            # Test retrieval
            print("Testing mentor retrieval...")
            retrieved_mentor = db.query(MentorProfile).filter(
                MentorProfile.user_id == user.id
            ).first()
            
            if retrieved_mentor:
                print(f"✓ Mentor created successfully!")
                print(f"  Bio: {retrieved_mentor.bio}")
                print(f"  Experience: {retrieved_mentor.experience_years} years")
                print(f"  Hourly Rate: ${retrieved_mentor.hourly_rate}")
                print(f"  Languages: {json.loads(retrieved_mentor.languages) if retrieved_mentor.languages else []}")
                print(f"  Expertise Subjects: {[s.name for s in retrieved_mentor.expertise_subjects]}")
                print(f"  Preferred Subjects: {[s.name for s in retrieved_mentor.preferred_subjects]}")
                
                # Clean up test data
                db.delete(retrieved_mentor)
                db.delete(user)
                db.commit()
                print("✓ Test data cleaned up")
                
            else:
                print("✗ Failed to retrieve mentor")
                
        except Exception as e:
            db.rollback()
            print(f"✗ Test failed: {e}")
            raise


if __name__ == "__main__":
    test_mentor_creation()
