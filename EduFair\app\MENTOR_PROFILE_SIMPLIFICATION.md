# Mentor Profile Simplification

## Overview
The MentorProfile model has been simplified to include only the essential fields as requested. This document outlines the changes made and how to use the new structure.

## Fields Kept

### Core Profile Information
- `profile_image_url` - Profile picture URL
- `bio` - Professional bio/description
- `experience_years` - Years of experience
- `hourly_rate` - Hourly rate for mentoring

### Subject Relationships (Many-to-Many)
- `expertise_subjects` - Subjects the mentor has expertise in
- `preferred_subjects` - Subjects the mentor prefers to teach

### Additional Information
- `languages` - Languages spoken (stored as JSON array)
- `availability_hours` - Available time slots (stored as JSON object)

## Database Changes

### New Tables Created
1. `mentor_expertise_subjects` - Many-to-many relationship between mentors and their expertise subjects
2. `mentor_preferred_subjects` - Many-to-many relationship between mentors and their preferred subjects

### Fields Removed
- `full_name` (moved to User model if needed)
- `expertise_areas` (replaced with many-to-many relationship)
- `education`
- `certifications`
- `phone`
- `linkedin_url`
- `website`
- `current_position`
- `current_organization`
- `preferred_subjects` (replaced with many-to-many relationship)
- `is_verified`
- `verification_status`
- `verification_notes`
- `verified_at`
- `verified_by`
- `resume_url`
- `portfolio_url`
- `rating`
- `total_reviews`
- `competitions_checked`

## Schema Changes

### MentorRegistrationCreate
```python
class MentorRegistrationCreate(UserCreate):
    # Essential mentor profile details
    bio: Optional[str] = Field(None, max_length=2000)
    experience_years: Optional[int] = Field(None, ge=0, le=50)
    hourly_rate: Optional[Decimal] = Field(None, ge=0)
    
    # Subject relationships (UUIDs)
    expertise_subject_ids: List[UUID] = Field(default=[])
    preferred_subject_ids: List[UUID] = Field(default=[])
    
    # Languages and availability
    languages: Optional[List[str]] = None
    availability_hours: Optional[Dict[str, Any]] = None
```

### MentorProfileOut
```python
class MentorProfileOut(BaseModel):
    id: UUID
    user_id: UUID
    bio: Optional[str]
    experience_years: Optional[int]
    hourly_rate: Optional[Decimal]
    languages: Optional[List[str]]
    availability_hours: Optional[Dict[str, Any]]
    profile_image_url: Optional[str]
    
    # Subject relationships
    expertise_subjects: Optional[List[Dict[str, Any]]] = None
    preferred_subjects: Optional[List[Dict[str, Any]]] = None
    
    created_at: datetime
    updated_at: datetime
```

## Usage Examples

### Creating a Mentor
```python
mentor_data = MentorRegistrationCreate(
    username="mentor123",
    email="<EMAIL>",
    mobile="+1234567890",
    password="securepassword",
    country="USA",
    bio="Experienced STEM mentor",
    experience_years=5,
    hourly_rate=50.00,
    expertise_subject_ids=[math_subject_id, physics_subject_id],
    preferred_subject_ids=[math_subject_id, chemistry_subject_id],
    languages=["English", "Spanish"],
    availability_hours={
        "monday": ["09:00-12:00", "14:00-17:00"],
        "tuesday": ["09:00-12:00"]
    }
)
```

### Updating a Mentor Profile
```python
update_data = MentorProfileUpdate(
    bio="Updated bio",
    hourly_rate=60.00,
    expertise_subject_ids=[new_subject_id],
    languages=["English", "French"]
)
```

## Migration

### Running the Migration
```bash
python migrations/simplify_mentor_profile.py
```

### Rolling Back (if needed)
```bash
python migrations/simplify_mentor_profile.py rollback
```

## Testing

A test script is provided to verify the new model works correctly:
```bash
python test_simplified_mentor.py
```

## Benefits of Simplification

1. **Cleaner Data Model** - Only essential fields are kept
2. **Proper Relationships** - Subject relationships are now properly normalized
3. **Better Performance** - Fewer fields mean faster queries
4. **Easier Maintenance** - Less complex model is easier to maintain
5. **Flexible Subject Management** - Easy to add/remove subject relationships

## Notes

- The `languages` field is stored as JSON to allow multiple languages
- The `availability_hours` field is stored as JSON to allow flexible scheduling
- Subject relationships use proper many-to-many tables for better data integrity
- All removed fields are backed up in `mentor_profile_backup` table during migration
