from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query
from sqlalchemy.orm import Session
from uuid import UUID

# Import CRUD functions
from Cruds.Institute.Mentor import (
    register_mentor, get_mentor_by_id, get_mentors, update_mentor_profile,
    verify_mentor, apply_to_institute, invite_mentor,
    respond_to_mentor_application_by_institute, accept_institute_invitation_by_mentor,
    get_mentor_associations, get_institute_associations, get_institute_invites, get_mentor_applications
)

# Import Schemas
from Schemas.Institute.Mentor import (
    MentorRegistrationBase, MentorProfileUpdate, MentorVerificationUpdate,
    MentorUserOut, MentorDetailedOut, MentorListResponse,
    MentorSearchFilter, MentorApplicationCreate, MentorApplicationOut,
    InstituteInvitationCreate, InstituteInvitationOut,
    InviteResponseCreate, ApplicationResponseCreate, MentorInstituteAssociationOut,
    MentorInstituteAssociationDetailedOut, AssociationSearchFilter
)

# Import dependencies
from config.session import get_db
from config.security import oauth2_scheme
from config.deps import get_current_user
from config.permission import require_type

router = APIRouter()


# Mentor Routes
@router.post("/register", response_model=MentorUserOut)
def register_mentor_route(
    mentor_data: MentorRegistrationBase,
    db: Session = Depends(get_db)
):
    """Register a new mentor"""
    return register_mentor(db, mentor_data)


@router.get("/list", response_model=MentorListResponse)
def get_list_of_mentors(
    search: Optional[str] = Query(None, description="Search term"),
    country: Optional[str] = Query(None, description="Filter by country"),
    verified_only: bool = Query(True, description="Show only verified mentors"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentors list"""
    try:
        skip = (page - 1) * size
        return get_mentors(db, skip, size, search)
    except Exception as e:
        print(f"Error in get_mentors route: {e}")
        # Return empty response on error
        from Schemas.Institute.Mentor import MentorListResponse
        return MentorListResponse(
            mentors=[],
            total=0,
            page=page,
            size=size,
            has_next=False,
            has_prev=False
        )





# Authenticated Mentor Routes
@router.get("/profile", response_model=MentorDetailedOut)
def get_my_mentor_profile(
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Get current mentor's profile"""
    current_user = get_current_user(token, db)
    return get_mentor_by_id(db, current_user.id)


@router.put("/profile", response_model=MentorDetailedOut)
def update_my_mentor_profile(
    profile_update: MentorProfileUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Update current mentor's profile"""
    current_user = get_current_user(token, db)
    return update_mentor_profile(db, current_user.id, profile_update)




# Mentor-Institute Association Routes
@router.post("/apply-to-institute", response_model=MentorApplicationOut)
def apply_to_institute_route(
    application: MentorApplicationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Mentor applies to join an institute"""
    current_user = get_current_user(token, db)
    return apply_to_institute(db, current_user.id, application)


@router.post("/invite/mentor", response_model=InstituteInvitationOut)
def invite_mentor_route(
    invitation: InstituteInvitationCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute invites a mentor to join"""
    current_user = get_current_user(token, db)
    return invite_mentor(db, current_user.id, invitation)


@router.post("/invites/{invite_id}/respond", response_model=MentorInstituteAssociationOut)
def respond_to_invite_route(
    invite_id: UUID,
    response: InviteResponseCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("mentor"))
):
    """Mentor responds to an institute invitation"""
    current_user = get_current_user(token, db)
    return accept_institute_invitation_by_mentor(db, current_user.id, invite_id, response)


@router.post("/applications/{application_id}/respond", response_model=MentorInstituteAssociationOut)
def respond_to_application_route(
    application_id: UUID,
    response: ApplicationResponseCreate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Institute responds to a mentor application"""
    current_user = get_current_user(token, db)
    return respond_to_mentor_application_by_institute(db, current_user.id, application_id, response)


@router.get("/invites", response_model=List[InstituteInvitationOut])
def get_institute_invites_route(
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get invites sent by institute"""
    current_user = get_current_user(token, db)
    return get_institute_invites(db, current_user.id, skip, limit, status)


@router.get("/applications", response_model=List[MentorApplicationOut])
def get_mentor_applications_route(
    status: Optional[str] = Query(None, description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("institute"))
):
    """Get applications received by institute"""
    current_user = get_current_user(token, db)
    return get_mentor_applications(db, current_user.id, skip, limit, status)


@router.get("/my-associations", response_model=List[MentorInstituteAssociationDetailedOut])
def get_my_associations(
    status: Optional[str] = Query(None, description="Filter by status"),
    association_type: Optional[str] = Query(None, description="Filter by association type"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get current user's associations (mentor or institute)"""
    current_user = get_current_user(token, db)

    filters = AssociationSearchFilter(
        status=status,
        association_type=association_type
    )

    if current_user.user_type == "mentor":
        return get_mentor_associations(db, current_user.id, filters, skip, limit)
    elif current_user.user_type == "institute":
        return get_institute_associations(db, current_user.id, filters, skip, limit)
    else:
        raise HTTPException(status_code=403, detail="Only mentors and institutes can view associations")


@router.get("/institute/{institute_id}/mentors", response_model=List[MentorInstituteAssociationDetailedOut])
def get_institute_mentors(
    institute_id: UUID,
    status: Optional[str] = Query("active", description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(50, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentors associated with an institute"""
    filters = AssociationSearchFilter(status=status)
    return get_institute_associations(db, institute_id, filters, skip, limit)


@router.get("/mentor/{mentor_id}/institutes", response_model=List[MentorInstituteAssociationDetailedOut])
def get_mentor_institutes(
    mentor_id: UUID,
    status: Optional[str] = Query("active", description="Filter by status"),
    skip: int = Query(0, ge=0),
    limit: int = Query(20, ge=1, le=100),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get institutes associated with a mentor"""
    filters = AssociationSearchFilter(status=status)
    return get_mentor_associations(db, mentor_id, filters, skip, limit)


# Admin Routes
@router.get("/admin/all", response_model=MentorListResponse)
def get_all_mentors_admin(
    search: Optional[str] = Query(None, description="Search term"),
    verification_status: Optional[str] = Query(None, description="Filter by verification status"),
    country: Optional[str] = Query(None, description="Filter by country"),
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(50, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Get all mentors (admin only)"""
    filters = MentorSearchFilter(
        search=search,
        verification_status=verification_status,
        country=country
    )

    skip = (page - 1) * size
    return get_mentors(db, filters, skip, size)


@router.put("/admin/{mentor_id}/verify", response_model=MentorDetailedOut)
def verify_mentor_admin(
    mentor_id: UUID,
    verification_update: MentorVerificationUpdate,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme),
    _ = Depends(require_type("admin"))
):
    """Verify or reject mentor (admin only)"""
    current_user = get_current_user(token, db)
    return verify_mentor(db, mentor_id, verification_update, current_user.id)


# Search and Discovery Routes
@router.get("/search/by-expertise/{expertise}")
def get_mentors_by_expertise(
    expertise: str,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentors by expertise area"""
    filters = MentorSearchFilter(expertise_areas=[expertise], is_verified=True)
    skip = (page - 1) * size
    return get_mentors(db, filters, skip, size)


@router.get("/search/by-country/{country}")
def get_mentors_by_country(
    country: str,
    page: int = Query(1, ge=1, description="Page number"),
    size: int = Query(20, ge=1, le=100, description="Page size"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentors by country"""
    filters = MentorSearchFilter(country=country, is_verified=True)
    skip = (page - 1) * size
    return get_mentors(db, filters, skip, size)


@router.get("/featured")
def get_featured_mentors(
    limit: int = Query(10, ge=1, le=50, description="Number of featured mentors"),
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get featured mentors"""
    filters = MentorSearchFilter(is_verified=True, rating_min=4.0)
    return get_mentors(db, filters, 0, limit)


# Individual mentor route - MUST BE LAST to avoid conflicts
@router.get("/{mentor_id}", response_model=MentorDetailedOut)
def get_mentor(
    mentor_id: UUID,
    db: Session = Depends(get_db),
    token: str = Depends(oauth2_scheme)
):
    """Get mentor details (authenticated users)"""
    return get_mentor_by_id(db, mentor_id)
