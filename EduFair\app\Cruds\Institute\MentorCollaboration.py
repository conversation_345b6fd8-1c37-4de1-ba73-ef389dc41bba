"""
Easy-to-use CRUD operations for mentor-institute collaboration
Simple, intuitive functions with clear naming
"""

from typing import List, Optional
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from fastapi import HTTPException
from datetime import datetime, timezone, timedelta
import uuid

# Import Models
from Models.users import (
    User, UserTypeEnum, MentorProfile, InstituteProfile,
    MentorInstituteInvite, MentorInstituteApplication, MentorInstituteAssociation
)

# Import Schemas
from Schemas.Institute.MentorCollaboration import (
    SendInvitationRequest, InvitationDetails, RespondToInvitationRequest,
    SubmitApplicationRequest, ApplicationDetails, ReviewApplicationRequest,
    CollaborationDetails, UpdateCollaborationRequest,
    InvitationListResponse, ApplicationListResponse, CollaborationListResponse,
    CollaborationSummary
)


# === INVITATION OPERATIONS ===

def send_invitation_to_mentor(
    db: Session,
    institute_id: uuid.UUID,
    request: SendInvitationRequest
) -> InvitationDetails:
    """Institute sends invitation to a mentor"""
    
    # Verify institute exists and is verified
    institute = db.query(User).options(
        joinedload(User.institute_profile)
    ).filter(
        User.id == institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()
    
    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")
    
    if not institute.institute_profile.is_verified:
        raise HTTPException(status_code=400, detail="Only verified institutes can send invitations")
    
    # Check if mentor exists
    mentor = db.query(User).filter(
        User.email == request.mentor_email,
        User.user_type == UserTypeEnum.mentor
    ).first()
    
    # Check for existing pending invitation
    existing_invite = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.mentor_email == request.mentor_email,
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).first()
    
    if existing_invite:
        raise HTTPException(status_code=400, detail="Pending invitation already exists for this mentor")
    
    # Check for existing active collaboration
    if mentor:
        existing_collaboration = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.mentor_id == mentor.id,
            MentorInstituteAssociation.institute_id == institute_id,
            MentorInstituteAssociation.status == "active"
        ).first()
        
        if existing_collaboration:
            raise HTTPException(status_code=400, detail="Active collaboration already exists with this mentor")
    
    # Create invitation
    invitation = MentorInstituteInvite(
        mentor_id=mentor.id if mentor else None,
        institute_id=institute_id,
        mentor_email=request.mentor_email,
        invitation_message=request.message,
        status="pending",
        proposed_hourly_rate=request.hourly_rate,
        proposed_hours_per_week=request.hours_per_week,
        expertise_areas_needed=request.subjects_needed or [],
        invited_at=datetime.now(timezone.utc),
        expires_at=datetime.now(timezone.utc) + timedelta(days=30)
    )
    
    db.add(invitation)
    db.commit()
    db.refresh(invitation)
    
    return InvitationDetails(
        id=invitation.id,
        mentor_email=invitation.mentor_email,
        mentor_name=f"{mentor.first_name} {mentor.last_name}" if mentor else None,
        institute_name=f"{institute.first_name} {institute.last_name}",
        message=invitation.invitation_message,
        status=invitation.status,
        hourly_rate=invitation.proposed_hourly_rate,
        hours_per_week=invitation.proposed_hours_per_week,
        subjects_needed=invitation.expertise_areas_needed or [],
        sent_at=invitation.invited_at,
        expires_at=invitation.expires_at,
        responded_at=invitation.responded_at
    )


def get_sent_invitations(
    db: Session,
    institute_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """Get invitations sent by institute"""
    
    skip = (page - 1) * size
    
    query = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id
    )
    
    if status_filter:
        query = query.filter(MentorInstituteInvite.status == status_filter)
    
    total = query.count()
    invitations = query.order_by(desc(MentorInstituteInvite.invited_at)).offset(skip).limit(size).all()
    
    # Convert to response format
    invitation_details = []
    for inv in invitations:
        mentor = inv.mentor
        institute = db.query(User).filter(User.id == inv.institute_id).first()
        
        invitation_details.append(InvitationDetails(
            id=inv.id,
            mentor_email=inv.mentor_email,
            mentor_name=f"{mentor.first_name} {mentor.last_name}" if mentor else None,
            institute_name=f"{institute.first_name} {institute.last_name}",
            message=inv.invitation_message,
            status=inv.status,
            hourly_rate=inv.proposed_hourly_rate,
            hours_per_week=inv.proposed_hours_per_week,
            subjects_needed=inv.expertise_areas_needed or [],
            sent_at=inv.invited_at,
            expires_at=inv.expires_at,
            responded_at=inv.responded_at
        ))
    
    return InvitationListResponse(
        invitations=invitation_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def get_received_invitations(
    db: Session,
    mentor_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> InvitationListResponse:
    """Get invitations received by mentor"""
    
    skip = (page - 1) * size
    
    query = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.mentor_id == mentor_id
    )
    
    if status_filter:
        query = query.filter(MentorInstituteInvite.status == status_filter)
    
    total = query.count()
    invitations = query.order_by(desc(MentorInstituteInvite.invited_at)).offset(skip).limit(size).all()
    
    # Convert to response format
    invitation_details = []
    for inv in invitations:
        mentor = inv.mentor
        institute = db.query(User).filter(User.id == inv.institute_id).first()
        
        invitation_details.append(InvitationDetails(
            id=inv.id,
            mentor_email=inv.mentor_email,
            mentor_name=f"{mentor.first_name} {mentor.last_name}" if mentor else None,
            institute_name=f"{institute.first_name} {institute.last_name}",
            message=inv.invitation_message,
            status=inv.status,
            hourly_rate=inv.proposed_hourly_rate,
            hours_per_week=inv.proposed_hours_per_week,
            subjects_needed=inv.expertise_areas_needed or [],
            sent_at=inv.invited_at,
            expires_at=inv.expires_at,
            responded_at=inv.responded_at
        ))
    
    return InvitationListResponse(
        invitations=invitation_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def respond_to_invitation(
    db: Session,
    mentor_id: uuid.UUID,
    invitation_id: uuid.UUID,
    response: RespondToInvitationRequest
) -> CollaborationDetails:
    """Mentor responds to an invitation"""
    
    # Find the invitation
    invitation = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.id == invitation_id,
        MentorInstituteInvite.mentor_id == mentor_id,
        MentorInstituteInvite.status == "pending"
    ).first()
    
    if not invitation:
        raise HTTPException(status_code=404, detail="Invitation not found or already processed")
    
    # Check if expired
    if invitation.expires_at and datetime.now(timezone.utc) > invitation.expires_at:
        invitation.status = "expired"
        db.commit()
        raise HTTPException(status_code=400, detail="Invitation has expired")
    
    # Update invitation
    invitation.responded_at = datetime.now(timezone.utc)
    invitation.response_message = response.message
    
    if response.accept:
        invitation.status = "accepted"
        
        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=mentor_id,
            institute_id=invitation.institute_id,
            status="active",
            hourly_rate=response.counter_hourly_rate or invitation.proposed_hourly_rate,
            hours_per_week=response.counter_hours_per_week or invitation.proposed_hours_per_week,
            start_date=datetime.now(timezone.utc),
            created_from_invite_id=invitation.id
        )
        
        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)
        
        # Get mentor and institute details
        mentor = db.query(User).filter(User.id == mentor_id).first()
        institute = db.query(User).filter(User.id == invitation.institute_id).first()
        
        return CollaborationDetails(
            id=collaboration.id,
            mentor_id=collaboration.mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            mentor_email=mentor.email,
            institute_id=collaboration.institute_id,
            institute_name=f"{institute.first_name} {institute.last_name}",
            status=collaboration.status,
            hourly_rate=collaboration.hourly_rate,
            hours_per_week=collaboration.hours_per_week,
            subjects=invitation.expertise_areas_needed or [],
            started_at=collaboration.start_date,
            ended_at=collaboration.end_date,
            total_hours_worked=0,
            total_earnings=0
        )
    else:
        invitation.status = "declined"
        db.commit()
        raise HTTPException(status_code=400, detail="Invitation declined")


def cancel_invitation(
    db: Session,
    institute_id: uuid.UUID,
    invitation_id: uuid.UUID
) -> bool:
    """Institute cancels a pending invitation"""
    
    invitation = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.id == invitation_id,
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).first()
    
    if not invitation:
        raise HTTPException(status_code=404, detail="Invitation not found or already processed")
    
    invitation.status = "expired"  # Mark as expired instead of deleting
    db.commit()
    
    return True


# === APPLICATION OPERATIONS ===

def submit_application_to_institute(
    db: Session,
    mentor_id: uuid.UUID,
    request: SubmitApplicationRequest
) -> ApplicationDetails:
    """Mentor submits application to institute"""

    # Verify mentor exists and is verified
    mentor = db.query(User).options(
        joinedload(User.mentor_profile)
    ).filter(
        User.id == mentor_id,
        User.user_type == UserTypeEnum.mentor
    ).first()

    if not mentor:
        raise HTTPException(status_code=404, detail="Mentor not found")

    if not mentor.mentor_profile.is_verified:
        raise HTTPException(status_code=400, detail="Only verified mentors can submit applications")

    # Verify institute exists
    institute = db.query(User).filter(
        User.id == request.institute_id,
        User.user_type == UserTypeEnum.institute
    ).first()

    if not institute:
        raise HTTPException(status_code=404, detail="Institute not found")

    # Check for existing pending application
    existing_app = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.mentor_id == mentor_id,
        MentorInstituteApplication.institute_id == request.institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if existing_app:
        raise HTTPException(status_code=400, detail="Pending application already exists")

    # Check for existing active collaboration
    existing_collaboration = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.mentor_id == mentor_id,
        MentorInstituteAssociation.institute_id == request.institute_id,
        MentorInstituteAssociation.status == "active"
    ).first()

    if existing_collaboration:
        raise HTTPException(status_code=400, detail="Active collaboration already exists")

    # Create application
    application = MentorInstituteApplication(
        mentor_id=mentor_id,
        institute_id=request.institute_id,
        application_message=request.message,
        status="pending",
        proposed_hourly_rate=request.hourly_rate,
        availability_hours=request.hours_available,
        applied_at=datetime.now(timezone.utc)
    )

    db.add(application)
    db.commit()
    db.refresh(application)

    return ApplicationDetails(
        id=application.id,
        mentor_id=application.mentor_id,
        mentor_name=f"{mentor.first_name} {mentor.last_name}",
        mentor_email=mentor.email,
        institute_name=f"{institute.first_name} {institute.last_name}",
        message=application.application_message,
        status=application.status,
        hourly_rate=application.proposed_hourly_rate,
        hours_available=application.availability_hours,
        subjects=request.subjects or [],
        submitted_at=application.applied_at,
        reviewed_at=application.responded_at,
        reviewer_message=application.response_message
    )


def get_submitted_applications(
    db: Session,
    mentor_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> ApplicationListResponse:
    """Get applications submitted by mentor"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.mentor_id == mentor_id
    )

    if status_filter:
        query = query.filter(MentorInstituteApplication.status == status_filter)

    total = query.count()
    applications = query.order_by(desc(MentorInstituteApplication.applied_at)).offset(skip).limit(size).all()

    # Convert to response format
    application_details = []
    for app in applications:
        mentor = app.mentor
        institute = db.query(User).filter(User.id == app.institute_id).first()

        application_details.append(ApplicationDetails(
            id=app.id,
            mentor_id=app.mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            mentor_email=mentor.email,
            institute_name=f"{institute.first_name} {institute.last_name}",
            message=app.application_message,
            status=app.status,
            hourly_rate=app.proposed_hourly_rate,
            hours_available=app.availability_hours,
            subjects=[],  # Would need to store this separately
            submitted_at=app.applied_at,
            reviewed_at=app.responded_at,
            reviewer_message=app.response_message
        ))

    return ApplicationListResponse(
        applications=application_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def get_received_applications(
    db: Session,
    institute_id: uuid.UUID,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> ApplicationListResponse:
    """Get applications received by institute"""

    skip = (page - 1) * size

    query = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.institute_id == institute_id
    )

    if status_filter:
        query = query.filter(MentorInstituteApplication.status == status_filter)

    total = query.count()
    applications = query.order_by(desc(MentorInstituteApplication.applied_at)).offset(skip).limit(size).all()

    # Convert to response format
    application_details = []
    for app in applications:
        mentor = app.mentor
        institute = db.query(User).filter(User.id == app.institute_id).first()

        application_details.append(ApplicationDetails(
            id=app.id,
            mentor_id=app.mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            mentor_email=mentor.email,
            institute_name=f"{institute.first_name} {institute.last_name}",
            message=app.application_message,
            status=app.status,
            hourly_rate=app.proposed_hourly_rate,
            hours_available=app.availability_hours,
            subjects=[],  # Would need to store this separately
            submitted_at=app.applied_at,
            reviewed_at=app.responded_at,
            reviewer_message=app.response_message
        ))

    return ApplicationListResponse(
        applications=application_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def review_application(
    db: Session,
    institute_id: uuid.UUID,
    application_id: uuid.UUID,
    review: ReviewApplicationRequest
) -> CollaborationDetails:
    """Institute reviews a mentor application"""

    # Find the application
    application = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.id == application_id,
        MentorInstituteApplication.institute_id == institute_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if not application:
        raise HTTPException(status_code=404, detail="Application not found or already processed")

    # Update application
    application.responded_at = datetime.now(timezone.utc)
    application.response_message = review.message

    if review.approve:
        application.status = "approved"

        # Create collaboration
        collaboration = MentorInstituteAssociation(
            mentor_id=application.mentor_id,
            institute_id=institute_id,
            status="active",
            hourly_rate=review.approved_hourly_rate or application.proposed_hourly_rate,
            hours_per_week=review.approved_hours_per_week or application.availability_hours,
            start_date=datetime.now(timezone.utc),
            created_from_application_id=application.id
        )

        db.add(collaboration)
        db.commit()
        db.refresh(collaboration)

        # Get mentor and institute details
        mentor = db.query(User).filter(User.id == application.mentor_id).first()
        institute = db.query(User).filter(User.id == institute_id).first()

        return CollaborationDetails(
            id=collaboration.id,
            mentor_id=collaboration.mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            mentor_email=mentor.email,
            institute_id=collaboration.institute_id,
            institute_name=f"{institute.first_name} {institute.last_name}",
            status=collaboration.status,
            hourly_rate=collaboration.hourly_rate,
            hours_per_week=collaboration.hours_per_week,
            subjects=[],  # Would need to store this separately
            started_at=collaboration.start_date,
            ended_at=collaboration.end_date,
            total_hours_worked=0,
            total_earnings=0
        )
    else:
        application.status = "rejected"
        db.commit()
        raise HTTPException(status_code=400, detail="Application rejected")


def withdraw_application(
    db: Session,
    mentor_id: uuid.UUID,
    application_id: uuid.UUID
) -> bool:
    """Mentor withdraws their application"""

    application = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.id == application_id,
        MentorInstituteApplication.mentor_id == mentor_id,
        MentorInstituteApplication.status == "pending"
    ).first()

    if not application:
        raise HTTPException(status_code=404, detail="Application not found or already processed")

    application.status = "rejected"  # Mark as rejected instead of deleting
    application.responded_at = datetime.now(timezone.utc)
    application.response_message = "Withdrawn by mentor"
    db.commit()

    return True


# === COLLABORATION OPERATIONS ===

def get_active_collaborations(
    db: Session,
    user_id: uuid.UUID,
    user_type: str,
    page: int = 1,
    size: int = 20,
    status_filter: Optional[str] = None
) -> CollaborationListResponse:
    """Get collaborations for mentor or institute"""

    skip = (page - 1) * size

    if user_type == "mentor":
        query = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.mentor_id == user_id
        )
    else:  # institute
        query = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.institute_id == user_id
        )

    if status_filter:
        query = query.filter(MentorInstituteAssociation.status == status_filter)

    total = query.count()
    collaborations = query.order_by(desc(MentorInstituteAssociation.start_date)).offset(skip).limit(size).all()

    # Convert to response format
    collaboration_details = []
    for collab in collaborations:
        mentor = db.query(User).filter(User.id == collab.mentor_id).first()
        institute = db.query(User).filter(User.id == collab.institute_id).first()

        collaboration_details.append(CollaborationDetails(
            id=collab.id,
            mentor_id=collab.mentor_id,
            mentor_name=f"{mentor.first_name} {mentor.last_name}",
            mentor_email=mentor.email,
            institute_id=collab.institute_id,
            institute_name=f"{institute.first_name} {institute.last_name}",
            status=collab.status,
            hourly_rate=collab.hourly_rate,
            hours_per_week=collab.hours_per_week,
            subjects=[],  # Would need to store this separately
            started_at=collab.start_date,
            ended_at=collab.end_date,
            total_hours_worked=0,  # Would need to calculate from time tracking
            total_earnings=0  # Would need to calculate from time tracking
        ))

    return CollaborationListResponse(
        collaborations=collaboration_details,
        total=total,
        page=page,
        size=size,
        has_next=(skip + size) < total,
        has_prev=page > 1
    )


def update_collaboration(
    db: Session,
    collaboration_id: uuid.UUID,
    user_id: uuid.UUID,
    user_type: str,
    update_request: UpdateCollaborationRequest
) -> CollaborationDetails:
    """Update collaboration details"""

    # Find collaboration
    if user_type == "mentor":
        collaboration = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.id == collaboration_id,
            MentorInstituteAssociation.mentor_id == user_id
        ).first()
    else:  # institute
        collaboration = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.id == collaboration_id,
            MentorInstituteAssociation.institute_id == user_id
        ).first()

    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")

    # Update fields
    if update_request.status is not None:
        collaboration.status = update_request.status
        if update_request.status == "ended" and not collaboration.end_date:
            collaboration.end_date = datetime.now(timezone.utc)

    if update_request.hourly_rate is not None:
        collaboration.hourly_rate = update_request.hourly_rate

    if update_request.hours_per_week is not None:
        collaboration.hours_per_week = update_request.hours_per_week

    if update_request.end_date is not None:
        collaboration.end_date = update_request.end_date
        if collaboration.status == "active":
            collaboration.status = "ended"

    collaboration.updated_at = datetime.now(timezone.utc)
    db.commit()
    db.refresh(collaboration)

    # Get mentor and institute details
    mentor = db.query(User).filter(User.id == collaboration.mentor_id).first()
    institute = db.query(User).filter(User.id == collaboration.institute_id).first()

    return CollaborationDetails(
        id=collaboration.id,
        mentor_id=collaboration.mentor_id,
        mentor_name=f"{mentor.first_name} {mentor.last_name}",
        mentor_email=mentor.email,
        institute_id=collaboration.institute_id,
        institute_name=f"{institute.first_name} {institute.last_name}",
        status=collaboration.status,
        hourly_rate=collaboration.hourly_rate,
        hours_per_week=collaboration.hours_per_week,
        subjects=[],
        started_at=collaboration.start_date,
        ended_at=collaboration.end_date,
        total_hours_worked=0,
        total_earnings=0
    )


def end_collaboration(
    db: Session,
    collaboration_id: uuid.UUID,
    user_id: uuid.UUID,
    user_type: str
) -> bool:
    """End a collaboration"""

    # Find collaboration
    if user_type == "mentor":
        collaboration = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.id == collaboration_id,
            MentorInstituteAssociation.mentor_id == user_id
        ).first()
    else:  # institute
        collaboration = db.query(MentorInstituteAssociation).filter(
            MentorInstituteAssociation.id == collaboration_id,
            MentorInstituteAssociation.institute_id == user_id
        ).first()

    if not collaboration:
        raise HTTPException(status_code=404, detail="Collaboration not found")

    if collaboration.status != "active":
        raise HTTPException(status_code=400, detail="Only active collaborations can be ended")

    collaboration.status = "ended"
    collaboration.end_date = datetime.now(timezone.utc)
    collaboration.updated_at = datetime.now(timezone.utc)
    db.commit()

    return True


def get_collaboration_summary(
    db: Session,
    institute_id: uuid.UUID
) -> CollaborationSummary:
    """Get collaboration summary for institute"""

    # Count active collaborations
    active_collaborations = db.query(MentorInstituteAssociation).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active"
    ).count()

    # Count pending invitations
    pending_invitations = db.query(MentorInstituteInvite).filter(
        MentorInstituteInvite.institute_id == institute_id,
        MentorInstituteInvite.status == "pending"
    ).count()

    # Count pending applications
    pending_applications = db.query(MentorInstituteApplication).filter(
        MentorInstituteApplication.institute_id == institute_id,
        MentorInstituteApplication.status == "pending"
    ).count()

    # Count total mentors (unique)
    total_mentors = db.query(MentorInstituteAssociation.mentor_id).filter(
        MentorInstituteAssociation.institute_id == institute_id
    ).distinct().count()

    # Calculate average hourly rate
    avg_rate = db.query(func.avg(MentorInstituteAssociation.hourly_rate)).filter(
        MentorInstituteAssociation.institute_id == institute_id,
        MentorInstituteAssociation.status == "active",
        MentorInstituteAssociation.hourly_rate.isnot(None)
    ).scalar()

    return CollaborationSummary(
        total_active_collaborations=active_collaborations,
        total_pending_invitations=pending_invitations,
        total_pending_applications=pending_applications,
        total_mentors=total_mentors,
        average_hourly_rate=avg_rate,
        total_hours_this_month=0  # Would need time tracking system
    )
